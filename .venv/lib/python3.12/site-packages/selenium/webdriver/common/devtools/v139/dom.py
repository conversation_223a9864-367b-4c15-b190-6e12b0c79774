# DO NOT EDIT THIS FILE!
#
# This file is generated from the CDP specification. If you need to make
# changes, edit the generator and regenerate all of the modules.
#
# CDP domain: DOM
from __future__ import annotations
from .util import event_class, T_JSON_DICT
from dataclasses import dataclass
import enum
import typing
from . import page
from . import runtime


class NodeId(int):
    '''
    Unique DOM node identifier.
    '''
    def to_json(self) -> int:
        return self

    @classmethod
    def from_json(cls, json: int) -> NodeId:
        return cls(json)

    def __repr__(self):
        return 'NodeId({})'.format(super().__repr__())


class BackendNodeId(int):
    '''
    Unique DOM node identifier used to reference a node that may not have been pushed to the
    front-end.
    '''
    def to_json(self) -> int:
        return self

    @classmethod
    def from_json(cls, json: int) -> BackendNodeId:
        return cls(json)

    def __repr__(self):
        return 'BackendNodeId({})'.format(super().__repr__())


@dataclass
class BackendNode:
    '''
    Backend node with a friendly name.
    '''
    #: ``Node``'s nodeType.
    node_type: int

    #: ``Node``'s nodeName.
    node_name: str

    backend_node_id: BackendNodeId

    def to_json(self):
        json = dict()
        json['nodeType'] = self.node_type
        json['nodeName'] = self.node_name
        json['backendNodeId'] = self.backend_node_id.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            node_type=int(json['nodeType']),
            node_name=str(json['nodeName']),
            backend_node_id=BackendNodeId.from_json(json['backendNodeId']),
        )


class PseudoType(enum.Enum):
    '''
    Pseudo element type.
    '''
    FIRST_LINE = "first-line"
    FIRST_LETTER = "first-letter"
    CHECKMARK = "checkmark"
    BEFORE = "before"
    AFTER = "after"
    PICKER_ICON = "picker-icon"
    MARKER = "marker"
    BACKDROP = "backdrop"
    COLUMN = "column"
    SELECTION = "selection"
    SEARCH_TEXT = "search-text"
    TARGET_TEXT = "target-text"
    SPELLING_ERROR = "spelling-error"
    GRAMMAR_ERROR = "grammar-error"
    HIGHLIGHT = "highlight"
    FIRST_LINE_INHERITED = "first-line-inherited"
    SCROLL_MARKER = "scroll-marker"
    SCROLL_MARKER_GROUP = "scroll-marker-group"
    SCROLL_BUTTON = "scroll-button"
    SCROLLBAR = "scrollbar"
    SCROLLBAR_THUMB = "scrollbar-thumb"
    SCROLLBAR_BUTTON = "scrollbar-button"
    SCROLLBAR_TRACK = "scrollbar-track"
    SCROLLBAR_TRACK_PIECE = "scrollbar-track-piece"
    SCROLLBAR_CORNER = "scrollbar-corner"
    RESIZER = "resizer"
    INPUT_LIST_BUTTON = "input-list-button"
    VIEW_TRANSITION = "view-transition"
    VIEW_TRANSITION_GROUP = "view-transition-group"
    VIEW_TRANSITION_IMAGE_PAIR = "view-transition-image-pair"
    VIEW_TRANSITION_GROUP_CHILDREN = "view-transition-group-children"
    VIEW_TRANSITION_OLD = "view-transition-old"
    VIEW_TRANSITION_NEW = "view-transition-new"
    PLACEHOLDER = "placeholder"
    FILE_SELECTOR_BUTTON = "file-selector-button"
    DETAILS_CONTENT = "details-content"
    PICKER = "picker"
    PERMISSION_ICON = "permission-icon"

    def to_json(self):
        return self.value

    @classmethod
    def from_json(cls, json):
        return cls(json)


class ShadowRootType(enum.Enum):
    '''
    Shadow root type.
    '''
    USER_AGENT = "user-agent"
    OPEN_ = "open"
    CLOSED = "closed"

    def to_json(self):
        return self.value

    @classmethod
    def from_json(cls, json):
        return cls(json)


class CompatibilityMode(enum.Enum):
    '''
    Document compatibility mode.
    '''
    QUIRKS_MODE = "QuirksMode"
    LIMITED_QUIRKS_MODE = "LimitedQuirksMode"
    NO_QUIRKS_MODE = "NoQuirksMode"

    def to_json(self):
        return self.value

    @classmethod
    def from_json(cls, json):
        return cls(json)


class PhysicalAxes(enum.Enum):
    '''
    ContainerSelector physical axes
    '''
    HORIZONTAL = "Horizontal"
    VERTICAL = "Vertical"
    BOTH = "Both"

    def to_json(self):
        return self.value

    @classmethod
    def from_json(cls, json):
        return cls(json)


class LogicalAxes(enum.Enum):
    '''
    ContainerSelector logical axes
    '''
    INLINE = "Inline"
    BLOCK = "Block"
    BOTH = "Both"

    def to_json(self):
        return self.value

    @classmethod
    def from_json(cls, json):
        return cls(json)


class ScrollOrientation(enum.Enum):
    '''
    Physical scroll orientation
    '''
    HORIZONTAL = "horizontal"
    VERTICAL = "vertical"

    def to_json(self):
        return self.value

    @classmethod
    def from_json(cls, json):
        return cls(json)


@dataclass
class Node:
    '''
    DOM interaction is implemented in terms of mirror objects that represent the actual DOM nodes.
    DOMNode is a base node mirror type.
    '''
    #: Node identifier that is passed into the rest of the DOM messages as the ``nodeId``. Backend
    #: will only push node with given ``id`` once. It is aware of all requested nodes and will only
    #: fire DOM events for nodes known to the client.
    node_id: NodeId

    #: The BackendNodeId for this node.
    backend_node_id: BackendNodeId

    #: ``Node``'s nodeType.
    node_type: int

    #: ``Node``'s nodeName.
    node_name: str

    #: ``Node``'s localName.
    local_name: str

    #: ``Node``'s nodeValue.
    node_value: str

    #: The id of the parent node if any.
    parent_id: typing.Optional[NodeId] = None

    #: Child count for ``Container`` nodes.
    child_node_count: typing.Optional[int] = None

    #: Child nodes of this node when requested with children.
    children: typing.Optional[typing.List[Node]] = None

    #: Attributes of the ``Element`` node in the form of flat array ``[name1, value1, name2, value2]``.
    attributes: typing.Optional[typing.List[str]] = None

    #: Document URL that ``Document`` or ``FrameOwner`` node points to.
    document_url: typing.Optional[str] = None

    #: Base URL that ``Document`` or ``FrameOwner`` node uses for URL completion.
    base_url: typing.Optional[str] = None

    #: ``DocumentType``'s publicId.
    public_id: typing.Optional[str] = None

    #: ``DocumentType``'s systemId.
    system_id: typing.Optional[str] = None

    #: ``DocumentType``'s internalSubset.
    internal_subset: typing.Optional[str] = None

    #: ``Document``'s XML version in case of XML documents.
    xml_version: typing.Optional[str] = None

    #: ``Attr``'s name.
    name: typing.Optional[str] = None

    #: ``Attr``'s value.
    value: typing.Optional[str] = None

    #: Pseudo element type for this node.
    pseudo_type: typing.Optional[PseudoType] = None

    #: Pseudo element identifier for this node. Only present if there is a
    #: valid pseudoType.
    pseudo_identifier: typing.Optional[str] = None

    #: Shadow root type.
    shadow_root_type: typing.Optional[ShadowRootType] = None

    #: Frame ID for frame owner elements.
    frame_id: typing.Optional[page.FrameId] = None

    #: Content document for frame owner elements.
    content_document: typing.Optional[Node] = None

    #: Shadow root list for given element host.
    shadow_roots: typing.Optional[typing.List[Node]] = None

    #: Content document fragment for template elements.
    template_content: typing.Optional[Node] = None

    #: Pseudo elements associated with this node.
    pseudo_elements: typing.Optional[typing.List[Node]] = None

    #: Deprecated, as the HTML Imports API has been removed (crbug.com/937746).
    #: This property used to return the imported document for the HTMLImport links.
    #: The property is always undefined now.
    imported_document: typing.Optional[Node] = None

    #: Distributed nodes for given insertion point.
    distributed_nodes: typing.Optional[typing.List[BackendNode]] = None

    #: Whether the node is SVG.
    is_svg: typing.Optional[bool] = None

    compatibility_mode: typing.Optional[CompatibilityMode] = None

    assigned_slot: typing.Optional[BackendNode] = None

    is_scrollable: typing.Optional[bool] = None

    def to_json(self):
        json = dict()
        json['nodeId'] = self.node_id.to_json()
        json['backendNodeId'] = self.backend_node_id.to_json()
        json['nodeType'] = self.node_type
        json['nodeName'] = self.node_name
        json['localName'] = self.local_name
        json['nodeValue'] = self.node_value
        if self.parent_id is not None:
            json['parentId'] = self.parent_id.to_json()
        if self.child_node_count is not None:
            json['childNodeCount'] = self.child_node_count
        if self.children is not None:
            json['children'] = [i.to_json() for i in self.children]
        if self.attributes is not None:
            json['attributes'] = [i for i in self.attributes]
        if self.document_url is not None:
            json['documentURL'] = self.document_url
        if self.base_url is not None:
            json['baseURL'] = self.base_url
        if self.public_id is not None:
            json['publicId'] = self.public_id
        if self.system_id is not None:
            json['systemId'] = self.system_id
        if self.internal_subset is not None:
            json['internalSubset'] = self.internal_subset
        if self.xml_version is not None:
            json['xmlVersion'] = self.xml_version
        if self.name is not None:
            json['name'] = self.name
        if self.value is not None:
            json['value'] = self.value
        if self.pseudo_type is not None:
            json['pseudoType'] = self.pseudo_type.to_json()
        if self.pseudo_identifier is not None:
            json['pseudoIdentifier'] = self.pseudo_identifier
        if self.shadow_root_type is not None:
            json['shadowRootType'] = self.shadow_root_type.to_json()
        if self.frame_id is not None:
            json['frameId'] = self.frame_id.to_json()
        if self.content_document is not None:
            json['contentDocument'] = self.content_document.to_json()
        if self.shadow_roots is not None:
            json['shadowRoots'] = [i.to_json() for i in self.shadow_roots]
        if self.template_content is not None:
            json['templateContent'] = self.template_content.to_json()
        if self.pseudo_elements is not None:
            json['pseudoElements'] = [i.to_json() for i in self.pseudo_elements]
        if self.imported_document is not None:
            json['importedDocument'] = self.imported_document.to_json()
        if self.distributed_nodes is not None:
            json['distributedNodes'] = [i.to_json() for i in self.distributed_nodes]
        if self.is_svg is not None:
            json['isSVG'] = self.is_svg
        if self.compatibility_mode is not None:
            json['compatibilityMode'] = self.compatibility_mode.to_json()
        if self.assigned_slot is not None:
            json['assignedSlot'] = self.assigned_slot.to_json()
        if self.is_scrollable is not None:
            json['isScrollable'] = self.is_scrollable
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            node_id=NodeId.from_json(json['nodeId']),
            backend_node_id=BackendNodeId.from_json(json['backendNodeId']),
            node_type=int(json['nodeType']),
            node_name=str(json['nodeName']),
            local_name=str(json['localName']),
            node_value=str(json['nodeValue']),
            parent_id=NodeId.from_json(json['parentId']) if 'parentId' in json else None,
            child_node_count=int(json['childNodeCount']) if 'childNodeCount' in json else None,
            children=[Node.from_json(i) for i in json['children']] if 'children' in json else None,
            attributes=[str(i) for i in json['attributes']] if 'attributes' in json else None,
            document_url=str(json['documentURL']) if 'documentURL' in json else None,
            base_url=str(json['baseURL']) if 'baseURL' in json else None,
            public_id=str(json['publicId']) if 'publicId' in json else None,
            system_id=str(json['systemId']) if 'systemId' in json else None,
            internal_subset=str(json['internalSubset']) if 'internalSubset' in json else None,
            xml_version=str(json['xmlVersion']) if 'xmlVersion' in json else None,
            name=str(json['name']) if 'name' in json else None,
            value=str(json['value']) if 'value' in json else None,
            pseudo_type=PseudoType.from_json(json['pseudoType']) if 'pseudoType' in json else None,
            pseudo_identifier=str(json['pseudoIdentifier']) if 'pseudoIdentifier' in json else None,
            shadow_root_type=ShadowRootType.from_json(json['shadowRootType']) if 'shadowRootType' in json else None,
            frame_id=page.FrameId.from_json(json['frameId']) if 'frameId' in json else None,
            content_document=Node.from_json(json['contentDocument']) if 'contentDocument' in json else None,
            shadow_roots=[Node.from_json(i) for i in json['shadowRoots']] if 'shadowRoots' in json else None,
            template_content=Node.from_json(json['templateContent']) if 'templateContent' in json else None,
            pseudo_elements=[Node.from_json(i) for i in json['pseudoElements']] if 'pseudoElements' in json else None,
            imported_document=Node.from_json(json['importedDocument']) if 'importedDocument' in json else None,
            distributed_nodes=[BackendNode.from_json(i) for i in json['distributedNodes']] if 'distributedNodes' in json else None,
            is_svg=bool(json['isSVG']) if 'isSVG' in json else None,
            compatibility_mode=CompatibilityMode.from_json(json['compatibilityMode']) if 'compatibilityMode' in json else None,
            assigned_slot=BackendNode.from_json(json['assignedSlot']) if 'assignedSlot' in json else None,
            is_scrollable=bool(json['isScrollable']) if 'isScrollable' in json else None,
        )


@dataclass
class DetachedElementInfo:
    '''
    A structure to hold the top-level node of a detached tree and an array of its retained descendants.
    '''
    tree_node: Node

    retained_node_ids: typing.List[NodeId]

    def to_json(self):
        json = dict()
        json['treeNode'] = self.tree_node.to_json()
        json['retainedNodeIds'] = [i.to_json() for i in self.retained_node_ids]
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            tree_node=Node.from_json(json['treeNode']),
            retained_node_ids=[NodeId.from_json(i) for i in json['retainedNodeIds']],
        )


@dataclass
class RGBA:
    '''
    A structure holding an RGBA color.
    '''
    #: The red component, in the [0-255] range.
    r: int

    #: The green component, in the [0-255] range.
    g: int

    #: The blue component, in the [0-255] range.
    b: int

    #: The alpha component, in the [0-1] range (default: 1).
    a: typing.Optional[float] = None

    def to_json(self):
        json = dict()
        json['r'] = self.r
        json['g'] = self.g
        json['b'] = self.b
        if self.a is not None:
            json['a'] = self.a
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            r=int(json['r']),
            g=int(json['g']),
            b=int(json['b']),
            a=float(json['a']) if 'a' in json else None,
        )


class Quad(list):
    '''
    An array of quad vertices, x immediately followed by y for each point, points clock-wise.
    '''
    def to_json(self) -> typing.List[float]:
        return self

    @classmethod
    def from_json(cls, json: typing.List[float]) -> Quad:
        return cls(json)

    def __repr__(self):
        return 'Quad({})'.format(super().__repr__())


@dataclass
class BoxModel:
    '''
    Box model.
    '''
    #: Content box
    content: Quad

    #: Padding box
    padding: Quad

    #: Border box
    border: Quad

    #: Margin box
    margin: Quad

    #: Node width
    width: int

    #: Node height
    height: int

    #: Shape outside coordinates
    shape_outside: typing.Optional[ShapeOutsideInfo] = None

    def to_json(self):
        json = dict()
        json['content'] = self.content.to_json()
        json['padding'] = self.padding.to_json()
        json['border'] = self.border.to_json()
        json['margin'] = self.margin.to_json()
        json['width'] = self.width
        json['height'] = self.height
        if self.shape_outside is not None:
            json['shapeOutside'] = self.shape_outside.to_json()
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            content=Quad.from_json(json['content']),
            padding=Quad.from_json(json['padding']),
            border=Quad.from_json(json['border']),
            margin=Quad.from_json(json['margin']),
            width=int(json['width']),
            height=int(json['height']),
            shape_outside=ShapeOutsideInfo.from_json(json['shapeOutside']) if 'shapeOutside' in json else None,
        )


@dataclass
class ShapeOutsideInfo:
    '''
    CSS Shape Outside details.
    '''
    #: Shape bounds
    bounds: Quad

    #: Shape coordinate details
    shape: typing.List[typing.Any]

    #: Margin shape bounds
    margin_shape: typing.List[typing.Any]

    def to_json(self):
        json = dict()
        json['bounds'] = self.bounds.to_json()
        json['shape'] = [i for i in self.shape]
        json['marginShape'] = [i for i in self.margin_shape]
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            bounds=Quad.from_json(json['bounds']),
            shape=[i for i in json['shape']],
            margin_shape=[i for i in json['marginShape']],
        )


@dataclass
class Rect:
    '''
    Rectangle.
    '''
    #: X coordinate
    x: float

    #: Y coordinate
    y: float

    #: Rectangle width
    width: float

    #: Rectangle height
    height: float

    def to_json(self):
        json = dict()
        json['x'] = self.x
        json['y'] = self.y
        json['width'] = self.width
        json['height'] = self.height
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            x=float(json['x']),
            y=float(json['y']),
            width=float(json['width']),
            height=float(json['height']),
        )


@dataclass
class CSSComputedStyleProperty:
    #: Computed style property name.
    name: str

    #: Computed style property value.
    value: str

    def to_json(self):
        json = dict()
        json['name'] = self.name
        json['value'] = self.value
        return json

    @classmethod
    def from_json(cls, json):
        return cls(
            name=str(json['name']),
            value=str(json['value']),
        )


def collect_class_names_from_subtree(
        node_id: NodeId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[str]]:
    '''
    Collects class names for the node with given id and all of it's child nodes.

    **EXPERIMENTAL**

    :param node_id: Id of the node to collect class names.
    :returns: Class name list.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.collectClassNamesFromSubtree',
        'params': params,
    }
    json = yield cmd_dict
    return [str(i) for i in json['classNames']]


def copy_to(
        node_id: NodeId,
        target_node_id: NodeId,
        insert_before_node_id: typing.Optional[NodeId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Creates a deep copy of the specified node and places it into the target container before the
    given anchor.

    **EXPERIMENTAL**

    :param node_id: Id of the node to copy.
    :param target_node_id: Id of the element to drop the copy into.
    :param insert_before_node_id: *(Optional)* Drop the copy before this node (if absent, the copy becomes the last child of ```targetNodeId```).
    :returns: Id of the node clone.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['targetNodeId'] = target_node_id.to_json()
    if insert_before_node_id is not None:
        params['insertBeforeNodeId'] = insert_before_node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.copyTo',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


def describe_node(
        node_id: typing.Optional[NodeId] = None,
        backend_node_id: typing.Optional[BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None,
        depth: typing.Optional[int] = None,
        pierce: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,Node]:
    '''
    Describes node given its id, does not require domain to be enabled. Does not start tracking any
    objects, can be used for automation.

    :param node_id: *(Optional)* Identifier of the node.
    :param backend_node_id: *(Optional)* Identifier of the backend node.
    :param object_id: *(Optional)* JavaScript object id of the node wrapper.
    :param depth: *(Optional)* The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0.
    :param pierce: *(Optional)* Whether or not iframes and shadow roots should be traversed when returning the subtree (default is false).
    :returns: Node description.
    '''
    params: T_JSON_DICT = dict()
    if node_id is not None:
        params['nodeId'] = node_id.to_json()
    if backend_node_id is not None:
        params['backendNodeId'] = backend_node_id.to_json()
    if object_id is not None:
        params['objectId'] = object_id.to_json()
    if depth is not None:
        params['depth'] = depth
    if pierce is not None:
        params['pierce'] = pierce
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.describeNode',
        'params': params,
    }
    json = yield cmd_dict
    return Node.from_json(json['node'])


def scroll_into_view_if_needed(
        node_id: typing.Optional[NodeId] = None,
        backend_node_id: typing.Optional[BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None,
        rect: typing.Optional[Rect] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Scrolls the specified rect of the given node into view if not already visible.
    Note: exactly one between nodeId, backendNodeId and objectId should be passed
    to identify the node.

    :param node_id: *(Optional)* Identifier of the node.
    :param backend_node_id: *(Optional)* Identifier of the backend node.
    :param object_id: *(Optional)* JavaScript object id of the node wrapper.
    :param rect: *(Optional)* The rect to be scrolled into view, relative to the node's border box, in CSS pixels. When omitted, center of the node will be used, similar to Element.scrollIntoView.
    '''
    params: T_JSON_DICT = dict()
    if node_id is not None:
        params['nodeId'] = node_id.to_json()
    if backend_node_id is not None:
        params['backendNodeId'] = backend_node_id.to_json()
    if object_id is not None:
        params['objectId'] = object_id.to_json()
    if rect is not None:
        params['rect'] = rect.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.scrollIntoViewIfNeeded',
        'params': params,
    }
    json = yield cmd_dict


def disable() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Disables DOM agent for the given page.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.disable',
    }
    json = yield cmd_dict


def discard_search_results(
        search_id: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Discards search results from the session with the given id. ``getSearchResults`` should no longer
    be called for that search.

    **EXPERIMENTAL**

    :param search_id: Unique search session identifier.
    '''
    params: T_JSON_DICT = dict()
    params['searchId'] = search_id
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.discardSearchResults',
        'params': params,
    }
    json = yield cmd_dict


def enable(
        include_whitespace: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Enables DOM agent for the given page.

    :param include_whitespace: **(EXPERIMENTAL)** *(Optional)* Whether to include whitespaces in the children array of returned Nodes.
    '''
    params: T_JSON_DICT = dict()
    if include_whitespace is not None:
        params['includeWhitespace'] = include_whitespace
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.enable',
        'params': params,
    }
    json = yield cmd_dict


def focus(
        node_id: typing.Optional[NodeId] = None,
        backend_node_id: typing.Optional[BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Focuses the given element.

    :param node_id: *(Optional)* Identifier of the node.
    :param backend_node_id: *(Optional)* Identifier of the backend node.
    :param object_id: *(Optional)* JavaScript object id of the node wrapper.
    '''
    params: T_JSON_DICT = dict()
    if node_id is not None:
        params['nodeId'] = node_id.to_json()
    if backend_node_id is not None:
        params['backendNodeId'] = backend_node_id.to_json()
    if object_id is not None:
        params['objectId'] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.focus',
        'params': params,
    }
    json = yield cmd_dict


def get_attributes(
        node_id: NodeId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[str]]:
    '''
    Returns attributes for the specified node.

    :param node_id: Id of the node to retrieve attributes for.
    :returns: An interleaved array of node attribute names and values.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getAttributes',
        'params': params,
    }
    json = yield cmd_dict
    return [str(i) for i in json['attributes']]


def get_box_model(
        node_id: typing.Optional[NodeId] = None,
        backend_node_id: typing.Optional[BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,BoxModel]:
    '''
    Returns boxes for the given node.

    :param node_id: *(Optional)* Identifier of the node.
    :param backend_node_id: *(Optional)* Identifier of the backend node.
    :param object_id: *(Optional)* JavaScript object id of the node wrapper.
    :returns: Box model for the node.
    '''
    params: T_JSON_DICT = dict()
    if node_id is not None:
        params['nodeId'] = node_id.to_json()
    if backend_node_id is not None:
        params['backendNodeId'] = backend_node_id.to_json()
    if object_id is not None:
        params['objectId'] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getBoxModel',
        'params': params,
    }
    json = yield cmd_dict
    return BoxModel.from_json(json['model'])


def get_content_quads(
        node_id: typing.Optional[NodeId] = None,
        backend_node_id: typing.Optional[BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[Quad]]:
    '''
    Returns quads that describe node position on the page. This method
    might return multiple quads for inline nodes.

    **EXPERIMENTAL**

    :param node_id: *(Optional)* Identifier of the node.
    :param backend_node_id: *(Optional)* Identifier of the backend node.
    :param object_id: *(Optional)* JavaScript object id of the node wrapper.
    :returns: Quads that describe node layout relative to viewport.
    '''
    params: T_JSON_DICT = dict()
    if node_id is not None:
        params['nodeId'] = node_id.to_json()
    if backend_node_id is not None:
        params['backendNodeId'] = backend_node_id.to_json()
    if object_id is not None:
        params['objectId'] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getContentQuads',
        'params': params,
    }
    json = yield cmd_dict
    return [Quad.from_json(i) for i in json['quads']]


def get_document(
        depth: typing.Optional[int] = None,
        pierce: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,Node]:
    '''
    Returns the root DOM node (and optionally the subtree) to the caller.
    Implicitly enables the DOM domain events for the current target.

    :param depth: *(Optional)* The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0.
    :param pierce: *(Optional)* Whether or not iframes and shadow roots should be traversed when returning the subtree (default is false).
    :returns: Resulting node.
    '''
    params: T_JSON_DICT = dict()
    if depth is not None:
        params['depth'] = depth
    if pierce is not None:
        params['pierce'] = pierce
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getDocument',
        'params': params,
    }
    json = yield cmd_dict
    return Node.from_json(json['root'])


def get_flattened_document(
        depth: typing.Optional[int] = None,
        pierce: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[Node]]:
    '''
    Returns the root DOM node (and optionally the subtree) to the caller.
    Deprecated, as it is not designed to work well with the rest of the DOM agent.
    Use DOMSnapshot.captureSnapshot instead.

    :param depth: *(Optional)* The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0.
    :param pierce: *(Optional)* Whether or not iframes and shadow roots should be traversed when returning the subtree (default is false).
    :returns: Resulting node.
    '''
    params: T_JSON_DICT = dict()
    if depth is not None:
        params['depth'] = depth
    if pierce is not None:
        params['pierce'] = pierce
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getFlattenedDocument',
        'params': params,
    }
    json = yield cmd_dict
    return [Node.from_json(i) for i in json['nodes']]


def get_nodes_for_subtree_by_style(
        node_id: NodeId,
        computed_styles: typing.List[CSSComputedStyleProperty],
        pierce: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[NodeId]]:
    '''
    Finds nodes with a given computed style in a subtree.

    **EXPERIMENTAL**

    :param node_id: Node ID pointing to the root of a subtree.
    :param computed_styles: The style to filter nodes by (includes nodes if any of properties matches).
    :param pierce: *(Optional)* Whether or not iframes and shadow roots in the same target should be traversed when returning the results (default is false).
    :returns: Resulting nodes.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['computedStyles'] = [i.to_json() for i in computed_styles]
    if pierce is not None:
        params['pierce'] = pierce
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getNodesForSubtreeByStyle',
        'params': params,
    }
    json = yield cmd_dict
    return [NodeId.from_json(i) for i in json['nodeIds']]


def get_node_for_location(
        x: int,
        y: int,
        include_user_agent_shadow_dom: typing.Optional[bool] = None,
        ignore_pointer_events_none: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[BackendNodeId, page.FrameId, typing.Optional[NodeId]]]:
    '''
    Returns node id at given location. Depending on whether DOM domain is enabled, nodeId is
    either returned or not.

    :param x: X coordinate.
    :param y: Y coordinate.
    :param include_user_agent_shadow_dom: *(Optional)* False to skip to the nearest non-UA shadow root ancestor (default: false).
    :param ignore_pointer_events_none: *(Optional)* Whether to ignore pointer-events: none on elements and hit test them.
    :returns: A tuple with the following items:

        0. **backendNodeId** - Resulting node.
        1. **frameId** - Frame this node belongs to.
        2. **nodeId** - *(Optional)* Id of the node at given coordinates, only when enabled and requested document.
    '''
    params: T_JSON_DICT = dict()
    params['x'] = x
    params['y'] = y
    if include_user_agent_shadow_dom is not None:
        params['includeUserAgentShadowDOM'] = include_user_agent_shadow_dom
    if ignore_pointer_events_none is not None:
        params['ignorePointerEventsNone'] = ignore_pointer_events_none
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getNodeForLocation',
        'params': params,
    }
    json = yield cmd_dict
    return (
        BackendNodeId.from_json(json['backendNodeId']),
        page.FrameId.from_json(json['frameId']),
        NodeId.from_json(json['nodeId']) if 'nodeId' in json else None
    )


def get_outer_html(
        node_id: typing.Optional[NodeId] = None,
        backend_node_id: typing.Optional[BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,str]:
    '''
    Returns node's HTML markup.

    :param node_id: *(Optional)* Identifier of the node.
    :param backend_node_id: *(Optional)* Identifier of the backend node.
    :param object_id: *(Optional)* JavaScript object id of the node wrapper.
    :returns: Outer HTML markup.
    '''
    params: T_JSON_DICT = dict()
    if node_id is not None:
        params['nodeId'] = node_id.to_json()
    if backend_node_id is not None:
        params['backendNodeId'] = backend_node_id.to_json()
    if object_id is not None:
        params['objectId'] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getOuterHTML',
        'params': params,
    }
    json = yield cmd_dict
    return str(json['outerHTML'])


def get_relayout_boundary(
        node_id: NodeId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Returns the id of the nearest ancestor that is a relayout boundary.

    **EXPERIMENTAL**

    :param node_id: Id of the node.
    :returns: Relayout boundary node id for the given node.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getRelayoutBoundary',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


def get_search_results(
        search_id: str,
        from_index: int,
        to_index: int
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[NodeId]]:
    '''
    Returns search results from given ``fromIndex`` to given ``toIndex`` from the search with the given
    identifier.

    **EXPERIMENTAL**

    :param search_id: Unique search session identifier.
    :param from_index: Start index of the search result to be returned.
    :param to_index: End index of the search result to be returned.
    :returns: Ids of the search result nodes.
    '''
    params: T_JSON_DICT = dict()
    params['searchId'] = search_id
    params['fromIndex'] = from_index
    params['toIndex'] = to_index
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getSearchResults',
        'params': params,
    }
    json = yield cmd_dict
    return [NodeId.from_json(i) for i in json['nodeIds']]


def hide_highlight() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Hides any highlight.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.hideHighlight',
    }
    json = yield cmd_dict


def highlight_node() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Highlights DOM node.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.highlightNode',
    }
    json = yield cmd_dict


def highlight_rect() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Highlights given rectangle.
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.highlightRect',
    }
    json = yield cmd_dict


def mark_undoable_state() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Marks last undoable state.

    **EXPERIMENTAL**
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.markUndoableState',
    }
    json = yield cmd_dict


def move_to(
        node_id: NodeId,
        target_node_id: NodeId,
        insert_before_node_id: typing.Optional[NodeId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Moves node into the new container, places it before the given anchor.

    :param node_id: Id of the node to move.
    :param target_node_id: Id of the element to drop the moved node into.
    :param insert_before_node_id: *(Optional)* Drop node before this one (if absent, the moved node becomes the last child of ```targetNodeId```).
    :returns: New id of the moved node.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['targetNodeId'] = target_node_id.to_json()
    if insert_before_node_id is not None:
        params['insertBeforeNodeId'] = insert_before_node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.moveTo',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


def perform_search(
        query: str,
        include_user_agent_shadow_dom: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[str, int]]:
    '''
    Searches for a given string in the DOM tree. Use ``getSearchResults`` to access search results or
    ``cancelSearch`` to end this search session.

    **EXPERIMENTAL**

    :param query: Plain text or query selector or XPath search query.
    :param include_user_agent_shadow_dom: *(Optional)* True to search in user agent shadow DOM.
    :returns: A tuple with the following items:

        0. **searchId** - Unique search session identifier.
        1. **resultCount** - Number of search results.
    '''
    params: T_JSON_DICT = dict()
    params['query'] = query
    if include_user_agent_shadow_dom is not None:
        params['includeUserAgentShadowDOM'] = include_user_agent_shadow_dom
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.performSearch',
        'params': params,
    }
    json = yield cmd_dict
    return (
        str(json['searchId']),
        int(json['resultCount'])
    )


def push_node_by_path_to_frontend(
        path: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Requests that the node is sent to the caller given its path. // FIXME, use XPath

    **EXPERIMENTAL**

    :param path: Path to node in the proprietary format.
    :returns: Id of the node for given path.
    '''
    params: T_JSON_DICT = dict()
    params['path'] = path
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.pushNodeByPathToFrontend',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


def push_nodes_by_backend_ids_to_frontend(
        backend_node_ids: typing.List[BackendNodeId]
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[NodeId]]:
    '''
    Requests that a batch of nodes is sent to the caller given their backend node ids.

    **EXPERIMENTAL**

    :param backend_node_ids: The array of backend node ids.
    :returns: The array of ids of pushed nodes that correspond to the backend ids specified in backendNodeIds.
    '''
    params: T_JSON_DICT = dict()
    params['backendNodeIds'] = [i.to_json() for i in backend_node_ids]
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.pushNodesByBackendIdsToFrontend',
        'params': params,
    }
    json = yield cmd_dict
    return [NodeId.from_json(i) for i in json['nodeIds']]


def query_selector(
        node_id: NodeId,
        selector: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Executes ``querySelector`` on a given node.

    :param node_id: Id of the node to query upon.
    :param selector: Selector string.
    :returns: Query selector result.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['selector'] = selector
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.querySelector',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


def query_selector_all(
        node_id: NodeId,
        selector: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[NodeId]]:
    '''
    Executes ``querySelectorAll`` on a given node.

    :param node_id: Id of the node to query upon.
    :param selector: Selector string.
    :returns: Query selector result.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['selector'] = selector
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.querySelectorAll',
        'params': params,
    }
    json = yield cmd_dict
    return [NodeId.from_json(i) for i in json['nodeIds']]


def get_top_layer_elements() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[NodeId]]:
    '''
    Returns NodeIds of current top layer elements.
    Top layer is rendered closest to the user within a viewport, therefore its elements always
    appear on top of all other content.

    **EXPERIMENTAL**

    :returns: NodeIds of top layer elements
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getTopLayerElements',
    }
    json = yield cmd_dict
    return [NodeId.from_json(i) for i in json['nodeIds']]


def get_element_by_relation(
        node_id: NodeId,
        relation: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Returns the NodeId of the matched element according to certain relations.

    **EXPERIMENTAL**

    :param node_id: Id of the node from which to query the relation.
    :param relation: Type of relation to get.
    :returns: NodeId of the element matching the queried relation.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['relation'] = relation
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getElementByRelation',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


def redo() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Re-does the last undone action.

    **EXPERIMENTAL**
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.redo',
    }
    json = yield cmd_dict


def remove_attribute(
        node_id: NodeId,
        name: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Removes attribute with given name from an element with given id.

    :param node_id: Id of the element to remove attribute from.
    :param name: Name of the attribute to remove.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['name'] = name
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.removeAttribute',
        'params': params,
    }
    json = yield cmd_dict


def remove_node(
        node_id: NodeId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Removes node with given id.

    :param node_id: Id of the node to remove.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.removeNode',
        'params': params,
    }
    json = yield cmd_dict


def request_child_nodes(
        node_id: NodeId,
        depth: typing.Optional[int] = None,
        pierce: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Requests that children of the node with given id are returned to the caller in form of
    ``setChildNodes`` events where not only immediate children are retrieved, but all children down to
    the specified depth.

    :param node_id: Id of the node to get children for.
    :param depth: *(Optional)* The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the entire subtree or provide an integer larger than 0.
    :param pierce: *(Optional)* Whether or not iframes and shadow roots should be traversed when returning the sub-tree (default is false).
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    if depth is not None:
        params['depth'] = depth
    if pierce is not None:
        params['pierce'] = pierce
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.requestChildNodes',
        'params': params,
    }
    json = yield cmd_dict


def request_node(
        object_id: runtime.RemoteObjectId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Requests that the node is sent to the caller given the JavaScript node object reference. All
    nodes that form the path from the node to the root are also sent to the client as a series of
    ``setChildNodes`` notifications.

    :param object_id: JavaScript object id to convert into node.
    :returns: Node id for given object.
    '''
    params: T_JSON_DICT = dict()
    params['objectId'] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.requestNode',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


def resolve_node(
        node_id: typing.Optional[NodeId] = None,
        backend_node_id: typing.Optional[BackendNodeId] = None,
        object_group: typing.Optional[str] = None,
        execution_context_id: typing.Optional[runtime.ExecutionContextId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,runtime.RemoteObject]:
    '''
    Resolves the JavaScript node object for a given NodeId or BackendNodeId.

    :param node_id: *(Optional)* Id of the node to resolve.
    :param backend_node_id: *(Optional)* Backend identifier of the node to resolve.
    :param object_group: *(Optional)* Symbolic group name that can be used to release multiple objects.
    :param execution_context_id: *(Optional)* Execution context in which to resolve the node.
    :returns: JavaScript object wrapper for given node.
    '''
    params: T_JSON_DICT = dict()
    if node_id is not None:
        params['nodeId'] = node_id.to_json()
    if backend_node_id is not None:
        params['backendNodeId'] = backend_node_id.to_json()
    if object_group is not None:
        params['objectGroup'] = object_group
    if execution_context_id is not None:
        params['executionContextId'] = execution_context_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.resolveNode',
        'params': params,
    }
    json = yield cmd_dict
    return runtime.RemoteObject.from_json(json['object'])


def set_attribute_value(
        node_id: NodeId,
        name: str,
        value: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Sets attribute for an element with given id.

    :param node_id: Id of the element to set attribute for.
    :param name: Attribute name.
    :param value: Attribute value.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['name'] = name
    params['value'] = value
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.setAttributeValue',
        'params': params,
    }
    json = yield cmd_dict


def set_attributes_as_text(
        node_id: NodeId,
        text: str,
        name: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Sets attributes on element with given id. This method is useful when user edits some existing
    attribute value and types in several attribute name/value pairs.

    :param node_id: Id of the element to set attributes for.
    :param text: Text with a number of attributes. Will parse this text using HTML parser.
    :param name: *(Optional)* Attribute name to replace with new attributes derived from text in case text parsed successfully.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['text'] = text
    if name is not None:
        params['name'] = name
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.setAttributesAsText',
        'params': params,
    }
    json = yield cmd_dict


def set_file_input_files(
        files: typing.List[str],
        node_id: typing.Optional[NodeId] = None,
        backend_node_id: typing.Optional[BackendNodeId] = None,
        object_id: typing.Optional[runtime.RemoteObjectId] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Sets files for the given file input element.

    :param files: Array of file paths to set.
    :param node_id: *(Optional)* Identifier of the node.
    :param backend_node_id: *(Optional)* Identifier of the backend node.
    :param object_id: *(Optional)* JavaScript object id of the node wrapper.
    '''
    params: T_JSON_DICT = dict()
    params['files'] = [i for i in files]
    if node_id is not None:
        params['nodeId'] = node_id.to_json()
    if backend_node_id is not None:
        params['backendNodeId'] = backend_node_id.to_json()
    if object_id is not None:
        params['objectId'] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.setFileInputFiles',
        'params': params,
    }
    json = yield cmd_dict


def set_node_stack_traces_enabled(
        enable: bool
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Sets if stack traces should be captured for Nodes. See ``Node.getNodeStackTraces``. Default is disabled.

    **EXPERIMENTAL**

    :param enable: Enable or disable.
    '''
    params: T_JSON_DICT = dict()
    params['enable'] = enable
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.setNodeStackTracesEnabled',
        'params': params,
    }
    json = yield cmd_dict


def get_node_stack_traces(
        node_id: NodeId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Optional[runtime.StackTrace]]:
    '''
    Gets stack traces associated with a Node. As of now, only provides stack trace for Node creation.

    **EXPERIMENTAL**

    :param node_id: Id of the node to get stack traces for.
    :returns: *(Optional)* Creation stack trace, if available.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getNodeStackTraces',
        'params': params,
    }
    json = yield cmd_dict
    return runtime.StackTrace.from_json(json['creation']) if 'creation' in json else None


def get_file_info(
        object_id: runtime.RemoteObjectId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,str]:
    '''
    Returns file information for the given
    File wrapper.

    **EXPERIMENTAL**

    :param object_id: JavaScript object id of the node wrapper.
    :returns: 
    '''
    params: T_JSON_DICT = dict()
    params['objectId'] = object_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getFileInfo',
        'params': params,
    }
    json = yield cmd_dict
    return str(json['path'])


def get_detached_dom_nodes() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[DetachedElementInfo]]:
    '''
    Returns list of detached nodes

    **EXPERIMENTAL**

    :returns: The list of detached nodes
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getDetachedDomNodes',
    }
    json = yield cmd_dict
    return [DetachedElementInfo.from_json(i) for i in json['detachedNodes']]


def set_inspected_node(
        node_id: NodeId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Enables console to refer to the node with given id via $x (see Command Line API for more details
    $x functions).

    **EXPERIMENTAL**

    :param node_id: DOM node id to be accessible by means of $x command line API.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.setInspectedNode',
        'params': params,
    }
    json = yield cmd_dict


def set_node_name(
        node_id: NodeId,
        name: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Sets node name for a node with given id.

    :param node_id: Id of the node to set name for.
    :param name: New node's name.
    :returns: New node's id.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['name'] = name
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.setNodeName',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


def set_node_value(
        node_id: NodeId,
        value: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Sets node value for a node with given id.

    :param node_id: Id of the node to set value for.
    :param value: New node's value.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['value'] = value
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.setNodeValue',
        'params': params,
    }
    json = yield cmd_dict


def set_outer_html(
        node_id: NodeId,
        outer_html: str
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Sets node HTML markup, returns new node id.

    :param node_id: Id of the node to set markup for.
    :param outer_html: Outer HTML markup to set.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    params['outerHTML'] = outer_html
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.setOuterHTML',
        'params': params,
    }
    json = yield cmd_dict


def undo() -> typing.Generator[T_JSON_DICT,T_JSON_DICT,None]:
    '''
    Undoes the last performed action.

    **EXPERIMENTAL**
    '''
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.undo',
    }
    json = yield cmd_dict


def get_frame_owner(
        frame_id: page.FrameId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Tuple[BackendNodeId, typing.Optional[NodeId]]]:
    '''
    Returns iframe node that owns iframe with the given domain.

    **EXPERIMENTAL**

    :param frame_id:
    :returns: A tuple with the following items:

        0. **backendNodeId** - Resulting node.
        1. **nodeId** - *(Optional)* Id of the node at given coordinates, only when enabled and requested document.
    '''
    params: T_JSON_DICT = dict()
    params['frameId'] = frame_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getFrameOwner',
        'params': params,
    }
    json = yield cmd_dict
    return (
        BackendNodeId.from_json(json['backendNodeId']),
        NodeId.from_json(json['nodeId']) if 'nodeId' in json else None
    )


def get_container_for_node(
        node_id: NodeId,
        container_name: typing.Optional[str] = None,
        physical_axes: typing.Optional[PhysicalAxes] = None,
        logical_axes: typing.Optional[LogicalAxes] = None,
        queries_scroll_state: typing.Optional[bool] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.Optional[NodeId]]:
    '''
    Returns the query container of the given node based on container query
    conditions: containerName, physical and logical axes, and whether it queries
    scroll-state. If no axes are provided and queriesScrollState is false, the
    style container is returned, which is the direct parent or the closest
    element with a matching container-name.

    **EXPERIMENTAL**

    :param node_id:
    :param container_name: *(Optional)*
    :param physical_axes: *(Optional)*
    :param logical_axes: *(Optional)*
    :param queries_scroll_state: *(Optional)*
    :returns: *(Optional)* The container node for the given node, or null if not found.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    if container_name is not None:
        params['containerName'] = container_name
    if physical_axes is not None:
        params['physicalAxes'] = physical_axes.to_json()
    if logical_axes is not None:
        params['logicalAxes'] = logical_axes.to_json()
    if queries_scroll_state is not None:
        params['queriesScrollState'] = queries_scroll_state
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getContainerForNode',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId']) if 'nodeId' in json else None


def get_querying_descendants_for_container(
        node_id: NodeId
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,typing.List[NodeId]]:
    '''
    Returns the descendants of a container query container that have
    container queries against this container.

    **EXPERIMENTAL**

    :param node_id: Id of the container node to find querying descendants from.
    :returns: Descendant nodes with container queries against the given container.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getQueryingDescendantsForContainer',
        'params': params,
    }
    json = yield cmd_dict
    return [NodeId.from_json(i) for i in json['nodeIds']]


def get_anchor_element(
        node_id: NodeId,
        anchor_specifier: typing.Optional[str] = None
    ) -> typing.Generator[T_JSON_DICT,T_JSON_DICT,NodeId]:
    '''
    Returns the target anchor element of the given anchor query according to
    https://www.w3.org/TR/css-anchor-position-1/#target.

    **EXPERIMENTAL**

    :param node_id: Id of the positioned element from which to find the anchor.
    :param anchor_specifier: *(Optional)* An optional anchor specifier, as defined in https://www.w3.org/TR/css-anchor-position-1/#anchor-specifier. If not provided, it will return the implicit anchor element for the given positioned element.
    :returns: The anchor element of the given anchor query.
    '''
    params: T_JSON_DICT = dict()
    params['nodeId'] = node_id.to_json()
    if anchor_specifier is not None:
        params['anchorSpecifier'] = anchor_specifier
    cmd_dict: T_JSON_DICT = {
        'method': 'DOM.getAnchorElement',
        'params': params,
    }
    json = yield cmd_dict
    return NodeId.from_json(json['nodeId'])


@event_class('DOM.attributeModified')
@dataclass
class AttributeModified:
    '''
    Fired when ``Element``'s attribute is modified.
    '''
    #: Id of the node that has changed.
    node_id: NodeId
    #: Attribute name.
    name: str
    #: Attribute value.
    value: str

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> AttributeModified:
        return cls(
            node_id=NodeId.from_json(json['nodeId']),
            name=str(json['name']),
            value=str(json['value'])
        )


@event_class('DOM.attributeRemoved')
@dataclass
class AttributeRemoved:
    '''
    Fired when ``Element``'s attribute is removed.
    '''
    #: Id of the node that has changed.
    node_id: NodeId
    #: A ttribute name.
    name: str

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> AttributeRemoved:
        return cls(
            node_id=NodeId.from_json(json['nodeId']),
            name=str(json['name'])
        )


@event_class('DOM.characterDataModified')
@dataclass
class CharacterDataModified:
    '''
    Mirrors ``DOMCharacterDataModified`` event.
    '''
    #: Id of the node that has changed.
    node_id: NodeId
    #: New text value.
    character_data: str

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> CharacterDataModified:
        return cls(
            node_id=NodeId.from_json(json['nodeId']),
            character_data=str(json['characterData'])
        )


@event_class('DOM.childNodeCountUpdated')
@dataclass
class ChildNodeCountUpdated:
    '''
    Fired when ``Container``'s child node count has changed.
    '''
    #: Id of the node that has changed.
    node_id: NodeId
    #: New node count.
    child_node_count: int

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ChildNodeCountUpdated:
        return cls(
            node_id=NodeId.from_json(json['nodeId']),
            child_node_count=int(json['childNodeCount'])
        )


@event_class('DOM.childNodeInserted')
@dataclass
class ChildNodeInserted:
    '''
    Mirrors ``DOMNodeInserted`` event.
    '''
    #: Id of the node that has changed.
    parent_node_id: NodeId
    #: Id of the previous sibling.
    previous_node_id: NodeId
    #: Inserted node data.
    node: Node

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ChildNodeInserted:
        return cls(
            parent_node_id=NodeId.from_json(json['parentNodeId']),
            previous_node_id=NodeId.from_json(json['previousNodeId']),
            node=Node.from_json(json['node'])
        )


@event_class('DOM.childNodeRemoved')
@dataclass
class ChildNodeRemoved:
    '''
    Mirrors ``DOMNodeRemoved`` event.
    '''
    #: Parent id.
    parent_node_id: NodeId
    #: Id of the node that has been removed.
    node_id: NodeId

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ChildNodeRemoved:
        return cls(
            parent_node_id=NodeId.from_json(json['parentNodeId']),
            node_id=NodeId.from_json(json['nodeId'])
        )


@event_class('DOM.distributedNodesUpdated')
@dataclass
class DistributedNodesUpdated:
    '''
    **EXPERIMENTAL**

    Called when distribution is changed.
    '''
    #: Insertion point where distributed nodes were updated.
    insertion_point_id: NodeId
    #: Distributed nodes for given insertion point.
    distributed_nodes: typing.List[BackendNode]

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> DistributedNodesUpdated:
        return cls(
            insertion_point_id=NodeId.from_json(json['insertionPointId']),
            distributed_nodes=[BackendNode.from_json(i) for i in json['distributedNodes']]
        )


@event_class('DOM.documentUpdated')
@dataclass
class DocumentUpdated:
    '''
    Fired when ``Document`` has been totally updated. Node ids are no longer valid.
    '''


    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> DocumentUpdated:
        return cls(

        )


@event_class('DOM.inlineStyleInvalidated')
@dataclass
class InlineStyleInvalidated:
    '''
    **EXPERIMENTAL**

    Fired when ``Element``'s inline style is modified via a CSS property modification.
    '''
    #: Ids of the nodes for which the inline styles have been invalidated.
    node_ids: typing.List[NodeId]

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> InlineStyleInvalidated:
        return cls(
            node_ids=[NodeId.from_json(i) for i in json['nodeIds']]
        )


@event_class('DOM.pseudoElementAdded')
@dataclass
class PseudoElementAdded:
    '''
    **EXPERIMENTAL**

    Called when a pseudo element is added to an element.
    '''
    #: Pseudo element's parent element id.
    parent_id: NodeId
    #: The added pseudo element.
    pseudo_element: Node

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> PseudoElementAdded:
        return cls(
            parent_id=NodeId.from_json(json['parentId']),
            pseudo_element=Node.from_json(json['pseudoElement'])
        )


@event_class('DOM.topLayerElementsUpdated')
@dataclass
class TopLayerElementsUpdated:
    '''
    **EXPERIMENTAL**

    Called when top layer elements are changed.
    '''


    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> TopLayerElementsUpdated:
        return cls(

        )


@event_class('DOM.scrollableFlagUpdated')
@dataclass
class ScrollableFlagUpdated:
    '''
    **EXPERIMENTAL**

    Fired when a node's scrollability state changes.
    '''
    #: The id of the node.
    node_id: dom.NodeId
    #: If the node is scrollable.
    is_scrollable: bool

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ScrollableFlagUpdated:
        return cls(
            node_id=dom.NodeId.from_json(json['nodeId']),
            is_scrollable=bool(json['isScrollable'])
        )


@event_class('DOM.pseudoElementRemoved')
@dataclass
class PseudoElementRemoved:
    '''
    **EXPERIMENTAL**

    Called when a pseudo element is removed from an element.
    '''
    #: Pseudo element's parent element id.
    parent_id: NodeId
    #: The removed pseudo element id.
    pseudo_element_id: NodeId

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> PseudoElementRemoved:
        return cls(
            parent_id=NodeId.from_json(json['parentId']),
            pseudo_element_id=NodeId.from_json(json['pseudoElementId'])
        )


@event_class('DOM.setChildNodes')
@dataclass
class SetChildNodes:
    '''
    Fired when backend wants to provide client with the missing DOM structure. This happens upon
    most of the calls requesting node ids.
    '''
    #: Parent node id to populate with children.
    parent_id: NodeId
    #: Child nodes array.
    nodes: typing.List[Node]

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> SetChildNodes:
        return cls(
            parent_id=NodeId.from_json(json['parentId']),
            nodes=[Node.from_json(i) for i in json['nodes']]
        )


@event_class('DOM.shadowRootPopped')
@dataclass
class ShadowRootPopped:
    '''
    **EXPERIMENTAL**

    Called when shadow root is popped from the element.
    '''
    #: Host element id.
    host_id: NodeId
    #: Shadow root id.
    root_id: NodeId

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ShadowRootPopped:
        return cls(
            host_id=NodeId.from_json(json['hostId']),
            root_id=NodeId.from_json(json['rootId'])
        )


@event_class('DOM.shadowRootPushed')
@dataclass
class ShadowRootPushed:
    '''
    **EXPERIMENTAL**

    Called when shadow root is pushed into the element.
    '''
    #: Host element id.
    host_id: NodeId
    #: Shadow root.
    root: Node

    @classmethod
    def from_json(cls, json: T_JSON_DICT) -> ShadowRootPushed:
        return cls(
            host_id=NodeId.from_json(json['hostId']),
            root=Node.from_json(json['root'])
        )
