import React, { useEffect, useState } from "react";

export default function BillList({ goBack }) {
  const [bills, setBills] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch("http://127.0.0.1:8000/api/run-file")
      .then((res) => res.json())
      .then((data) => {
        setBills(data.results || []);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, []);

  const downloadAll = () => {
    bills.forEach((bill) => {
      if (bill.status.includes("✅")) {
        const a = document.createElement("a");
        a.href = `http://127.0.0.1:8000/static/${bill.refNumber}.pdf`;
        a.download = `${bill.refNumber}.pdf`;
        a.click();
      }
    });
  };

  return (
    <div>
      <button onClick={goBack}>← Back</button>
      {loading && <p>⏳ Downloading...</p>}
      {bills.length > 0 && (
        <>
          <table>
            <thead>
              <tr>
                <th>Reference</th>
                <th>Status</th>
                <th>View PDF</th>
              </tr>
            </thead>
            <tbody>
              {bills.map((bill) => (
                <tr key={bill.refNumber}>
                  <td>{bill.refNumber}</td>
                  <td>{bill.status}</td>
                  <td>
                    {bill.status.includes("✅") && (
                      <a
                        href={`http://127.0.0.1:8000/static/${bill.refNumber}.pdf`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        View
                      </a>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <button onClick={downloadAll}>Download All PDFs</button>
        </>
      )}
    </div>
  );
}
