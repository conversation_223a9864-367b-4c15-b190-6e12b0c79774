// src/runPython.jsx
export async function runPythonScript(refNumber) {
  const API_URL = "http://localhost:8000/api/run-python"; // FastAPI endpoint

  const response = await fetch(API_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ refNumber }),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.detail || "Failed to download bill");
  }

  return await response.json();
}
