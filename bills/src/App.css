/* Reset default browser margins and padding */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Full page styling */
html, body, #root {
  width: 100%;
  height: 100%;
  font-family: Arial, sans-serif;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

/* Logo at top-left */
.logo {
  width: 200px;         /* slightly bigger */
  height: auto;
  position: absolute;   /* fixed to top-left */
  top: 20px;
  left: 20px;
}

/* Full page container for title + card */
.app-container {
  display: flex;
  flex-direction: column;
  align-items: center;       /* horizontal centering */
  justify-content: center;   /* vertical centering */
  padding: 20px;
  width: 100%;
  max-width: 500px;
}

/* Main title */
.main-title {
  font-size: 2rem;
  color: #333;              /* ✅ black text for title */
  margin-bottom: 40px;
  text-align: center;
}

/* Card around input + button */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding: 30px 40px;
  border-radius: 10px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Input group */
.input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

input[type="text"] {
  padding: 10px;
  font-size: 1rem;
  width: 250px;
  border: 1px solid black;
  border-radius: 5px;
  background-color: white;  /* ✅ always white background */
  color: black;             /* ✅ black text */
}

/* Button styling */
button {
  padding: 10px 20px;
  font-size: 1rem;
  background-color: #6a329f;
  color: white;             /* button text white */
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

button:hover {
  background-color: #50267a;  /* darker purple on hover */
  color: white;
}

/* Status text */
.status {
  font-size: 1rem;
  color: #555;
  text-align: center;
}
 