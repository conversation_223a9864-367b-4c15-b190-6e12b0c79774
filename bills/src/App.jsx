import React, { useState } from "react";
import "./App.css";
import DownloadBill from "./DownloadBill";
import BillList from "./BillList";

function App() {
  const [viewList, setViewList] = useState(false);

  return (
    <div className="app-container">
      <h1>Tele Taleem PowerBill</h1>
      {!viewList ? (
        <DownloadBill showList={() => setViewList(true)} />
      ) : (
        <BillList goBack={() => setViewList(false)} />
      )}
    </div>
  );
}

export default App;
