import React, { useState } from "react";

export default function DownloadBill({ showList }) {
  const [refNumber, setRefNumber] = useState("");
  const [status, setStatus] = useState("");

  const downloadBill = async () => {
    if (!refNumber) {
      setStatus("Enter reference number!");
      return;
    }
    setStatus("Downloading...");
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-single", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refNumber }),
      });
      const data = await res.json();
      if (res.ok) setStatus(`✅ Completed: ${data.file_path}`);
      else setStatus(`❌ ${data.detail}`);
    } catch {
      setStatus("❌ Error");
    }
  };

  return (
    <div>
      <h2>Download Single Bill</h2>
      <input
        type="text"
        placeholder="Enter Reference Number"
        value={refNumber}
        onChange={(e) => setRefNumber(e.target.value)}
      />
      <button onClick={downloadBill}>Download</button>
      <p>{status}</p>

      <hr />
      <button onClick={showList}>Download All Bills from File</button>
    </div>
  );
}
