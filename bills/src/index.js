const express = require("express");
const cors = require("cors");
const { exec } = require("child_process");
const path = require("path");

const app = express();
const PORT = 5000;

app.use(cors());
app.use(express.json());

// Endpoint to download bill
app.post("/download", (req, res) => {
  const { refNumber } = req.body;
  if (!refNumber) return res.status(400).json({ error: "refNumber required" });

  const pythonPath = "/home/<USER>/Desktop/soup/.venv/bin/python";
  const scriptPath = path.join(__dirname, "..", "aloo.py");

  exec(`${pythonPath} ${scriptPath} ${refNumber}`, (error, stdout, stderr) => {
    if (error) return res.status(500).json({ error: error.message });
    if (stderr) console.error(stderr);

    res.json({ message: "Bill downloaded successfully", output: stdout });
  });
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
