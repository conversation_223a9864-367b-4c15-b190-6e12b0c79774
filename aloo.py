import os
import sys
import time
import base64
from pathlib import Path
from typing import List

from fastapi import Fast<PERSON><PERSON>, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import <PERSON><PERSON>NResponse

import uvicorn
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# --------------------------
# FastAPI Setup
# --------------------------
app = FastAPI(title="Tele Taleem PowerBill API", version="3.3")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

BASE_DIR = Path(__file__).parent
download_dir = BASE_DIR / "IESCO_Bills"
download_dir.mkdir(parents=True, exist_ok=True)

# Serve PDFs
app.mount("/static", StaticFiles(directory=download_dir), name="static")

# --------------------------
# Selenium Driver
# --------------------------
def get_chrome_driver(headless=True):
    options = Options()
    if headless:
        options.add_argument("--headless=new")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-notifications")
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    return driver

# --------------------------
# Save PDF
# --------------------------
def save_pdf(driver, save_path):
    pdf = driver.execute_cdp_cmd("Page.printToPDF", {"printBackground": True, "format": "A4"})
    with open(save_path, "wb") as f:
        f.write(base64.b64decode(pdf["data"]))

# --------------------------
# Download single bill
# --------------------------
def download_single_bill(driver, ref_number: str):
    driver.get("https://iescobill.pk/")
    ref_input = WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.ID, "reference")))
    ref_input.clear()
    ref_input.send_keys(ref_number)

    driver.find_element(By.ID, "checkBill").click()
    time.sleep(2)

    # Wait for bill form button
    open_btn = WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, "form#billForm button[type='submit']"))
    )
    open_btn.click()

    # Switch to new window
    WebDriverWait(driver, 15).until(lambda d: len(d.window_handles) > 1)
    driver.switch_to.window(driver.window_handles[-1])

    # Wait for loader to disappear
    try:
        WebDriverWait(driver, 20).until(EC.invisibility_of_element_located((By.ID, "loader-container")))
    except:
        pass

    # Wait for print button
    WebDriverWait(driver, 15).until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button[onclick='window.print()']")))
    time.sleep(1)

    save_path = download_dir / f"{ref_number}.pdf"
    save_pdf(driver, str(save_path))

    driver.close()
    driver.switch_to.window(driver.window_handles[0])

# --------------------------
# Upload reference file
# --------------------------
@app.post("/api/upload-file")
async def upload_file(file: UploadFile = File(...)):
    file_path = BASE_DIR / file.filename
    with open(file_path, "wb") as f:
        f.write(await file.read())
    return {"filename": file.filename}

# --------------------------
# Run single bill
# --------------------------
@app.post("/api/run-single")
async def run_single(refNumber: dict):
    ref = refNumber.get("refNumber")
    if not ref:
        return JSONResponse(status_code=400, content={"detail": "Reference number missing"})

    driver = get_chrome_driver(headless=True)
    try:
        download_single_bill(driver, ref)
        return {"file_path": f"/static/{ref}.pdf", "status": "✅ Completed"}
    except Exception as e:
        return JSONResponse(status_code=500, content={"detail": str(e)})
    finally:
        driver.quit()

# --------------------------
# Run all bills
# --------------------------
@app.get("/api/run-file")
async def run_all_file():
    ref_file_candidates = [
        BASE_DIR / "ref_numbers.txt",
        Path.home() / "Desktop" / "ref_numbers.txt",
    ]
    ref_file_path = next((p for p in ref_file_candidates if p.exists()), None)
    if not ref_file_path:
        return JSONResponse(status_code=400, content={"error": "Reference file not found"})

    with open(ref_file_path, "r") as f:
        ref_numbers = [line.strip() for line in f if line.strip()]

    if not ref_numbers:
        return JSONResponse(status_code=400, content={"error": "No reference numbers found"})

    driver = get_chrome_driver(headless=True)
    results: List[dict] = []
    try:
        for ref in ref_numbers:
            try:
                download_single_bill(driver, ref)
                results.append({"refNumber": ref, "status": "✅ Completed"})
            except Exception as e:
                results.append({"refNumber": ref, "status": f"❌ Failed: {e}"})
        return {"results": results}
    finally:
        driver.quit()

# --------------------------
# Health check
# --------------------------
@app.get("/api/health")
async def health_check():
    return {"status": "OK", "message": "API is running 🚀"}

# --------------------------
# Main
# --------------------------
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
